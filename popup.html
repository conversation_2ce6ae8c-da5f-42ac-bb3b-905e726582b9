<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: Arial, sans-serif;
      font-size: 14px;
    }
    
    h2 {
      margin: 0 0 15px 0;
      color: #333;
      text-align: center;
    }
    
    .button-container {
      margin-bottom: 10px;
    }
    
    button {
      width: 100%;
      padding: 10px;
      margin: 5px 0;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background: #45a049;
    }
    
    button:active {
      background: #3d8b40;
    }
    
    .status {
      text-align: center;
      margin-top: 15px;
      padding: 10px;
      background: #f0f0f0;
      border-radius: 5px;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <h2>CRX Reactive Extension</h2>
  
  <div class="button-container">
    <button id="localBtn">本地变量值：0</button>
    <button id="contentABtn">A内容脚本变量值：0</button>
    <button id="contentBBtn">B内容脚本变量值：0</button>
    <button id="backgroundBtn">背景脚本变量值：0</button>
    <button id="popupBtn">popup变量值：0</button>
    <button id="optionsBtn">配置页变量值：0</button>
  </div>
  
  <div class="status">
    点击按钮增加对应变量的值
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
