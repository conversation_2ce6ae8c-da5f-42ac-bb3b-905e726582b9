// 配置页脚本
let optionsValue = 0;
let contentAValue = 0;
let contentBValue = 0;
let backgroundValue = 0;
let popupValue = 0;

// DOM元素引用（在DOM加载后初始化）
let localBtn,
  contentABtn,
  contentBBtn,
  backgroundBtn,
  popupBtn,
  optionsBtn,
  resetBtn;

// 初始化DOM元素引用
function initDOMElements() {
  localBtn = document.getElementById('localBtn');
  contentABtn = document.getElementById('contentABtn');
  contentBBtn = document.getElementById('contentBBtn');
  backgroundBtn = document.getElementById('backgroundBtn');
  popupBtn = document.getElementById('popupBtn');
  optionsBtn = document.getElementById('optionsBtn');
  resetBtn = document.getElementById('resetBtn');
}

// 更新按钮文本
function updateButtons() {
  if (!localBtn) return; // 确保DOM元素已初始化

  localBtn.textContent = `本地变量值：${optionsValue}`;
  contentABtn.textContent = `A内容脚本变量值：${contentAValue}`;
  contentBBtn.textContent = `B内容脚本变量值：${contentBValue}`;
  backgroundBtn.textContent = `背景脚本变量值：${backgroundValue}`;
  popupBtn.textContent = `popup变量值：${popupValue}`;
  optionsBtn.textContent = `配置页变量值：${optionsValue}`;
}

// 获取所有变量值
async function getAllValues() {
  try {
    // 获取背景脚本值
    const bgResponse = await chrome.runtime.sendMessage({
      action: 'getBackgroundValue',
    });
    if (bgResponse) backgroundValue = bgResponse.value;

    // 从存储中获取其他值
    const result = await chrome.storage.local.get([
      'optionsValue',
      'contentAValue',
      'contentBValue',
      'popupValue',
    ]);

    optionsValue = result.optionsValue || 0;
    contentAValue = result.contentAValue || 0;
    contentBValue = result.contentBValue || 0;
    popupValue = result.popupValue || 0;

    updateButtons();
  } catch (error) {
    console.log('Error getting values:', error);
  }
}

// 重置所有变量
async function resetAllValues() {
  if (confirm('确定要重置所有变量值吗？')) {
    try {
      // 重置本地变量
      optionsValue = 0;
      contentAValue = 0;
      contentBValue = 0;
      popupValue = 0;

      // 清除存储
      await chrome.storage.local.clear();

      // 重置背景脚本值（通过重新加载扩展实现）
      backgroundValue = 0;

      updateButtons();
      alert('所有变量值已重置！');
    } catch (error) {
      console.log('Error resetting values:', error);
      alert('重置失败，请重试');
    }
  }
}

// 设置按钮事件监听器
function setupEventListeners() {
  localBtn.addEventListener('click', async () => {
    optionsValue++;
    await chrome.storage.local.set({ optionsValue });
    updateButtons();
  });

  contentABtn.addEventListener('click', async () => {
    contentAValue++;
    await chrome.storage.local.set({ contentAValue });
    updateButtons();
  });

  contentBBtn.addEventListener('click', async () => {
    contentBValue++;
    await chrome.storage.local.set({ contentBValue });
    updateButtons();
  });

  backgroundBtn.addEventListener('click', async () => {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'incrementBackgroundValue',
      });
      if (response) backgroundValue = response.value;
      updateButtons();
    } catch (error) {
      console.log('Error incrementing background value:', error);
    }
  });

  popupBtn.addEventListener('click', async () => {
    popupValue++;
    await chrome.storage.local.set({ popupValue });
    updateButtons();
  });

  optionsBtn.addEventListener('click', async () => {
    optionsValue++;
    await chrome.storage.local.set({ optionsValue });
    updateButtons();
  });

  resetBtn.addEventListener('click', resetAllValues);
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.optionsValue) optionsValue = changes.optionsValue.newValue || 0;
    if (changes.contentAValue)
      contentAValue = changes.contentAValue.newValue || 0;
    if (changes.contentBValue)
      contentBValue = changes.contentBValue.newValue || 0;
    if (changes.popupValue) popupValue = changes.popupValue.newValue || 0;
    updateButtons();
  }
});

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((request) => {
  if (request.action === 'backgroundValueUpdated') {
    backgroundValue = request.value;
    updateButtons();
  }
});

// 初始化函数
async function initOptionsPage() {
  initDOMElements();
  setupEventListeners();
  await getAllValues();
}

// 初始化
document.addEventListener('DOMContentLoaded', initOptionsPage);

console.log('Options script loaded');
