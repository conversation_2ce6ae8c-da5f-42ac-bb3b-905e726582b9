// 背景脚本
let backgroundValue;

// 初始化背景脚本值
async function initBackgroundValue() {
  try {
    const result = await chrome.storage.local.get(['backgroundValue']);
    backgroundValue = result.backgroundValue || 0;
    console.log('Background value initialized to:', backgroundValue);
  } catch (error) {
    console.log('Error initializing background value:', error);
    backgroundValue = 0;
  }
}

// 保存背景脚本值到存储
async function saveBackgroundValue() {
  try {
    await chrome.storage.local.set({ backgroundValue });
  } catch (error) {
    console.log('Error saving background value:', error);
  }
}

// 监听来自其他脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getBackgroundValue') {
    sendResponse({ value: backgroundValue });
  } else if (request.action === 'incrementBackgroundValue') {
    backgroundValue++;
    console.log('Background value incremented to:', backgroundValue);

    // 保存到存储
    saveBackgroundValue();

    sendResponse({ value: backgroundValue });

    // 通知所有页面更新背景脚本的值
    chrome.runtime
      .sendMessage({
        action: 'backgroundValueUpdated',
        value: backgroundValue,
      })
      .catch(() => {
        // 忽略错误，因为可能没有接收者
      });
  }
  return true; // 保持消息通道开放
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started');
  initBackgroundValue();
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed');
  initBackgroundValue();
});

// 立即初始化
initBackgroundValue();

console.log('Background script loaded');
