// 背景脚本
let backgroundValue = 0;

// 监听来自其他脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getBackgroundValue') {
    sendResponse({ value: backgroundValue });
  } else if (request.action === 'incrementBackgroundValue') {
    backgroundValue++;
    console.log('Background value incremented to:', backgroundValue);
    sendResponse({ value: backgroundValue });
    
    // 通知所有页面更新背景脚本的值
    chrome.runtime.sendMessage({
      action: 'backgroundValueUpdated',
      value: backgroundValue
    }).catch(() => {
      // 忽略错误，因为可能没有接收者
    });
  }
  return true; // 保持消息通道开放
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started');
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed');
});

console.log('Background script loaded');
