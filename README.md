# CRX Reactive Extension

一个演示Chrome扩展各组件间通信的示例项目。

## 功能特性

### 扩展组件
1. **A内容脚本** - 仅在 www.baidu.com 运行
2. **B内容脚本** - 在所有网站运行（除百度外）
3. **背景脚本** - 后台服务工作者
4. **Popup页面** - 点击扩展图标显示的弹窗
5. **配置页** - 扩展的选项页面

### 变量系统
每个组件都维护一个数值型变量，所有组件都可以：
- 查看所有其他组件的变量值
- 增加任意组件的变量值
- 实时同步变量值变化

### 按钮功能
每个页面/脚本都包含6个按钮：
- 本地变量值：xxx
- A内容脚本变量值：xxx
- B内容脚本变量值：xxx
- 背景脚本变量值：xxx
- popup变量值：xxx
- 配置页变量值：xxx

点击任意按钮，对应的变量值+1，所有页面实时更新。

## 安装方法

1. 打开Chrome浏览器
2. 进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹

## 使用说明

### 内容脚本按钮位置
- **A内容脚本**：在百度网站左上角显示绿色按钮组
- **B内容脚本**：在其他网站右上角显示蓝色按钮组

### 访问其他页面
- **Popup页面**：点击扩展图标
- **配置页**：在扩展管理页面点击"扩展程序选项"

### 变量同步
所有变量值通过Chrome存储API和消息传递API实时同步，确保各组件间数据一致性。

## 技术实现

### 通信机制
- **Chrome Storage API**：持久化存储变量值
- **Chrome Runtime API**：组件间消息传递
- **Storage Change Listener**：监听存储变化实现实时更新

### 文件结构
```
├── manifest.json          # 扩展清单文件
├── background.js          # 背景脚本
├── content-script-a.js    # A内容脚本（百度专用）
├── content-script-b.js    # B内容脚本（通用）
├── popup.html            # Popup页面
├── popup.js              # Popup脚本
├── options.html          # 配置页面
├── options.js            # 配置页脚本
└── README.md             # 说明文档
```

## 注意事项

1. 需要Chrome扩展开发者模式
2. 图标文件需要自行添加（icon16.png, icon48.png, icon128.png）
3. 内容脚本会在页面顶部插入按钮，可能影响页面布局
4. 变量值在扩展重新加载后会重置（除存储在Chrome Storage中的值）

## 开发说明

本项目使用Manifest V3规范，兼容最新版Chrome浏览器。所有代码遵循现代JavaScript标准，使用async/await处理异步操作。
