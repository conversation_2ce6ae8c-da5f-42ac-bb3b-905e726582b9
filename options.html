<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>CRX Reactive Extension - 配置页</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    
    .container {
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #4CAF50;
      padding-bottom: 10px;
    }
    
    .button-section {
      margin-bottom: 20px;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #555;
      margin-bottom: 15px;
      text-align: center;
    }
    
    button {
      width: 100%;
      padding: 12px;
      margin: 8px 0;
      background: #FF9800;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: #F57C00;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    button:active {
      background: #E65100;
      transform: translateY(0);
    }
    
    .info-panel {
      background: #e8f5e8;
      padding: 15px;
      border-radius: 5px;
      margin-top: 20px;
      border-left: 4px solid #4CAF50;
    }
    
    .info-panel h3 {
      margin: 0 0 10px 0;
      color: #2E7D32;
    }
    
    .info-panel p {
      margin: 5px 0;
      color: #388E3C;
      font-size: 13px;
    }
    
    .reset-section {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
    }
    
    .reset-btn {
      background: #f44336 !important;
    }
    
    .reset-btn:hover {
      background: #d32f2f !important;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>CRX Reactive Extension 配置页</h1>
    
    <div class="button-section">
      <div class="section-title">变量控制按钮</div>
      <button id="localBtn">本地变量值：0</button>
      <button id="contentABtn">A内容脚本变量值：0</button>
      <button id="contentBBtn">B内容脚本变量值：0</button>
      <button id="backgroundBtn">背景脚本变量值：0</button>
      <button id="popupBtn">popup变量值：0</button>
      <button id="optionsBtn">配置页变量值：0</button>
    </div>
    
    <div class="info-panel">
      <h3>扩展功能说明</h3>
      <p>• A内容脚本：仅在 www.baidu.com 上运行，按钮显示在页面左上角</p>
      <p>• B内容脚本：在所有网站上运行（除百度外），按钮显示在页面右上角</p>
      <p>• 背景脚本：在扩展后台运行，处理跨页面通信</p>
      <p>• Popup页面：点击扩展图标时显示的弹窗</p>
      <p>• 配置页：当前页面，可通过扩展管理页面访问</p>
      <p>• 所有变量值都会实时同步到各个页面和脚本中</p>
    </div>
    
    <div class="reset-section">
      <div class="section-title">重置功能</div>
      <button id="resetBtn" class="reset-btn">重置所有变量值</button>
    </div>
  </div>
  
  <script src="options.js"></script>
</body>
</html>
