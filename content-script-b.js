// B内容脚本 - 针对所有网站（除了百度）
let contentBValue = 0;
let contentAValue = 0;
let backgroundValue = 0;
let popupValue = 0;
let optionsValue = 0;

// 创建按钮容器
function createButtonContainer() {
  const container = document.createElement('div');
  container.id = 'crx-reactive-buttons-b';
  container.style.cssText = `
    position: fixed;
    top: 0;
    right: 0;
    z-index: 10000;
    background: #e0e0e0;
    border: 2px solid #666;
    padding: 10px;
    font-family: Arial, sans-serif;
    font-size: 12px;
    max-width: 300px;
  `;
  
  // 创建标题
  const title = document.createElement('div');
  title.textContent = 'B内容脚本 (通用)';
  title.style.cssText = 'font-weight: bold; margin-bottom: 5px; color: #333;';
  container.appendChild(title);
  
  return container;
}

// 创建按钮
function createButton(text, onClick) {
  const button = document.createElement('button');
  button.textContent = text;
  button.style.cssText = `
    display: block;
    width: 100%;
    margin: 2px 0;
    padding: 5px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
  `;
  button.addEventListener('click', onClick);
  return button;
}

// 更新按钮文本
function updateButtons() {
  const container = document.getElementById('crx-reactive-buttons-b');
  if (!container) return;
  
  const buttons = container.querySelectorAll('button');
  if (buttons.length >= 6) {
    buttons[0].textContent = `本地变量值：${contentBValue}`;
    buttons[1].textContent = `A内容脚本变量值：${contentAValue}`;
    buttons[2].textContent = `B内容脚本变量值：${contentBValue}`;
    buttons[3].textContent = `背景脚本变量值：${backgroundValue}`;
    buttons[4].textContent = `popup变量值：${popupValue}`;
    buttons[5].textContent = `配置页变量值：${optionsValue}`;
  }
}

// 获取其他脚本的值
async function getOtherValues() {
  try {
    // 获取背景脚本值
    const bgResponse = await chrome.runtime.sendMessage({ action: 'getBackgroundValue' });
    if (bgResponse) backgroundValue = bgResponse.value;
    
    // 从存储中获取其他值
    const result = await chrome.storage.local.get(['contentAValue', 'popupValue', 'optionsValue']);
    contentAValue = result.contentAValue || 0;
    popupValue = result.popupValue || 0;
    optionsValue = result.optionsValue || 0;
  } catch (error) {
    console.log('Error getting other values:', error);
  }
}

// 初始化按钮
async function initButtons() {
  if (document.getElementById('crx-reactive-buttons-b')) return;
  
  await getOtherValues();
  
  const container = createButtonContainer();
  
  // 创建所有按钮
  const buttons = [
    createButton(`本地变量值：${contentBValue}`, () => {
      contentBValue++;
      chrome.storage.local.set({ contentBValue });
      updateButtons();
    }),
    createButton(`A内容脚本变量值：${contentAValue}`, async () => {
      contentAValue++;
      await chrome.storage.local.set({ contentAValue });
      updateButtons();
    }),
    createButton(`B内容脚本变量值：${contentBValue}`, () => {
      contentBValue++;
      chrome.storage.local.set({ contentBValue });
      updateButtons();
    }),
    createButton(`背景脚本变量值：${backgroundValue}`, async () => {
      try {
        const response = await chrome.runtime.sendMessage({ action: 'incrementBackgroundValue' });
        if (response) backgroundValue = response.value;
        updateButtons();
      } catch (error) {
        console.log('Error incrementing background value:', error);
      }
    }),
    createButton(`popup变量值：${popupValue}`, async () => {
      popupValue++;
      await chrome.storage.local.set({ popupValue });
      updateButtons();
    }),
    createButton(`配置页变量值：${optionsValue}`, async () => {
      optionsValue++;
      await chrome.storage.local.set({ optionsValue });
      updateButtons();
    })
  ];
  
  buttons.forEach(button => container.appendChild(button));
  document.body.appendChild(container);
}

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.contentAValue) contentAValue = changes.contentAValue.newValue || 0;
    if (changes.popupValue) popupValue = changes.popupValue.newValue || 0;
    if (changes.optionsValue) optionsValue = changes.optionsValue.newValue || 0;
    updateButtons();
  }
});

// 监听来自背景脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'backgroundValueUpdated') {
    backgroundValue = request.value;
    updateButtons();
  }
});

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initButtons);
} else {
  initButtons();
}

console.log('Content Script B loaded for all sites except baidu.com');
