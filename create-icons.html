<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h2>Chrome扩展图标生成器</h2>
    <p>点击下面的按钮生成并下载扩展所需的图标文件</p>
    
    <button onclick="createIcons()">生成图标</button>
    
    <div id="output"></div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 绘制背景
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(0, 0, size, size);
            
            // 绘制边框
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, size-2, size-2);
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size/4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('CRX', size/2, size/2);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function createIcons() {
            const sizes = [16, 48, 128];
            const output = document.getElementById('output');
            output.innerHTML = '<h3>生成的图标：</h3>';
            
            sizes.forEach(size => {
                const canvas = createIcon(size);
                const container = document.createElement('div');
                container.style.margin = '10px';
                container.innerHTML = `<p>icon${size}.png (${size}x${size})</p>`;
                container.appendChild(canvas);
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = `下载 icon${size}.png`;
                downloadBtn.onclick = () => downloadCanvas(canvas, `icon${size}.png`);
                container.appendChild(downloadBtn);
                
                output.appendChild(container);
            });
            
            output.innerHTML += '<p><strong>请将下载的图标文件放在扩展根目录中</strong></p>';
        }
    </script>
</body>
</html>
